import { useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextareaAutosize, TextField } from "@mui/material";
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

const deliverableSchema = z.object({
  milestone: z
    .string()
    .min(1, "Milestone is required")
    .max(200, "Milestone must be less than 200 characters"),
  dueDateContract: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  dueDatePlanned: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  achievedDate: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  paymentDue: z.number().min(0, "Payment due must be positive").optional(),
  invoiceDate: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  paymentReceivedDate: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  comments: z
    .string()
    .max(500, "Comments must be less than 500 characters")
    .optional(),
});

const formSchema = z.object({
  deliverables: z
    .array(deliverableSchema)
    .min(1, "At least one deliverable is required")
    .max(20, "Maximum 20 deliverables allowed"),
});

const ProgressReviewDeliverables = () => {

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      deliverables: [
        {
          milestone: "",
          dueDateContract: "",
          dueDatePlanned: "",
          achievedDate: "",
          paymentDue: 0,
          invoiceDate: "",
          paymentReceivedDate: "",
          comments: "",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "deliverables",
  });

  const addDeliverable = () => {
    append({
      milestone: "",
      dueDateContract: "",
      dueDatePlanned: "",
      achievedDate: "",
      paymentDue: 0,
      invoiceDate: "",
      paymentReceivedDate: "",
      comments: "",
    });
  };

  const removeDeliverable = (index) => {
    if (fields.length <= 1) {
      alert("At least one deliverable is required");
      return;
    }
    remove(index);
  };

  // const on

  return(
    <TableContainer>
      <Table>
        <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
          <TableRow>
            <TableCell sx={{ fontWeight: 'bold'}}>MileStone</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Due date as per contract</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Due date as planned</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Achieved date</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Payment due</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Invoice date</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Payment received date</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Comments</TableCell>
            <TableCell sx={{ fontWeight: 'bold'}}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {fields.map((field, index) => (
            <TableRow key={field.id}>
              {/* Milestone */}
              <TableCell>
                <TextField
                {...register(`deliverables.${index}.milestone`)}
                size="small"
                rows={2}
                />
              </TableCell>

              {/* Due date as per contract */}
              <TableCell>
                <TextField
                size="small"
                type="date"
                />
              </TableCell>

               {/* Due date as planned */}
              <TableCell>
                <TextField
                size="small"
                type="date"
                />
              </TableCell>

              {/* Achieved date */}
              <TableCell>
                <TextField
                size="small"
                type="date"
                />
              </TableCell>

              {/* Payment due */}
              <TableCell>
                <TextField
                size="small"
                />
              </TableCell>

              {/* Invoice date */}
              <TableCell>
                <TextField
                size="small"
                type="date"
                />
              </TableCell>

              {/* Payment received date */}
              <TableCell>
                <TextField
                size="small"
                type="date"
                />
              </TableCell>

              {/* Comments */}
              <TableCell>
                <TextareaAutosize
                aria-label="maximum height"
                minRows={2}
                maxRows={4}
                placeholder="Comments"
                />
              </TableCell>

              {/* Actions */}
              <TableCell>
                  <DeleteIcon 
                  color="error"
                  onClick={() => removeDeliverable(index)}
                  />
              </TableCell>

            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={addDeliverable}
        sx={{ mt: 2 }}
      >
        Add Deliverable
      </Button>
    </TableContainer>
  );
};

export default ProgressReviewDeliverables;
