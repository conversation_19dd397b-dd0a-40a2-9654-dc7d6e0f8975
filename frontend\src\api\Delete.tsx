import React, { useState } from "react";
import { useForm, use<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Container,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Alert,
  Stack,
  Divider,
  Tooltip,
  useTheme,
  alpha,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
  Description as FileTextIcon,
  Warning as AlertIcon,
} from "@mui/icons-material";

// Zod schema for validation
const milestoneSchema = z.object({
  id: z.number(),
  milestone: z
    .string()
    .min(1, "Milestone is required")
    .max(200, "Milestone must be less than 200 characters"),
  dueDateContract: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  dueDatePlanned: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  achievedDate: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  paymentDue: z.number().min(0, "Payment due must be positive").optional(),
  invoiceDate: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  paymentReceivedDate: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date format"),
  comments: z
    .string()
    .max(500, "Comments must be less than 500 characters")
    .optional(),
});

const formSchema = z.object({
  milestones: z
    .array(milestoneSchema)
    .min(1, "At least one milestone is required")
    .max(20, "Maximum 20 milestones allowed"),
});

const ProgressReviewDeliverables = () => {
  const [isEditing, setIsEditing] = useState(false);
  const theme = useTheme();

  const {
    control,
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid, isDirty },
    reset,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      milestones: [
        {
          id: 1,
          milestone: "Part A",
          dueDateContract: "",
          dueDatePlanned: "",
          achievedDate: "",
          paymentDue: 0,
          invoiceDate: "",
          paymentReceivedDate: "",
          comments: "",
        },
        {
          id: 2,
          milestone: "Advance payment",
          dueDateContract: "",
          dueDatePlanned: "",
          achievedDate: "",
          paymentDue: 300110.0,
          invoiceDate: "2014-05-14",
          paymentReceivedDate: "",
          comments: "Payment is not received against invoice.",
        },
        {
          id: 3,
          milestone: "Inception Report",
          dueDateContract: "2014-03-20",
          dueDatePlanned: "",
          achievedDate: "2014-05-14",
          paymentDue: 150055.0,
          invoiceDate: "2014-05-14",
          paymentReceivedDate: "",
          comments: "Payment is not received against invoice.",
        },
        {
          id: 4,
          milestone: "Detailed Project Report",
          dueDateContract: "2014-05-30",
          dueDatePlanned: "2015-01-15",
          achievedDate: "2015-02-15",
          paymentDue: 750275.0,
          invoiceDate: "2015-02-15",
          paymentReceivedDate: "",
          comments: "Payment is not received against invoice.",
        },
        {
          id: 5,
          milestone:
            "Submission of estimate, bill of quantities, specifications",
          dueDateContract: "2014-06-30",
          dueDatePlanned: "2015-02-15",
          achievedDate: "2015-02-15",
          paymentDue: 600220,
          invoiceDate: "2015-02-15",
          paymentReceivedDate: "",
          comments: "Payment is not received against invoice.",
        },
      ],
    },
    mode: "onChange",
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "milestones",
  });

  const watchedMilestones = watch("milestones");
  const totalPaymentDue = watchedMilestones.reduce(
    (sum, milestone) => sum + (Number(milestone.paymentDue) || 0),
    0
  );

  const onSubmit = async (data) => {
    try {
      console.log("Form Data:", data);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsEditing(false);
      alert("Progress review saved successfully!");
    } catch (error) {
      console.error("Submission error:", error);
      alert("Error saving data. Please try again.");
    }
  };

  const addMilestone = () => {
    const newId = Math.max(...fields.map((f) => f.id), 0) + 1;
    append({
      id: newId,
      milestone: "",
      dueDateContract: "",
      dueDatePlanned: "",
      achievedDate: "",
      paymentDue: 0,
      invoiceDate: "",
      paymentReceivedDate: "",
      comments: "",
    });
    setIsEditing(true);
  };

  const removeMilestone = (index) => {
    if (fields.length <= 1) {
      alert("At least one milestone is required");
      return;
    }
    if (window.confirm("Are you sure you want to remove this milestone?")) {
      remove(index);
    }
  };

  const toggleEdit = () => {
    if (isEditing) {
      reset();
    }
    setIsEditing(!isEditing);
  };

  const formatCurrency = (amount) => {
    if (!amount) return "₹0";
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-IN", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  const getFieldError = (fieldPath) => {
    const pathArray = fieldPath.split(".");
    let error = errors;
    for (const key of pathArray) {
      error = error?.[key];
    }
    return error?.message;
  };

  const hasFieldError = (fieldPath) => {
    return !!getFieldError(fieldPath);
  };

  const getPaymentStatus = (paymentDue, paymentReceived) => {
    if (!paymentDue || paymentDue === 0) return "N/A";
    if (paymentReceived) return "Received";
    return "Pending";
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case "Received":
        return "success";
      case "Pending":
        return "error";
      default:
        return "default";
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Card sx={{ mb: 3 }} elevation={2}>
        <CardContent>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="flex-start"
            mb={3}
          >
            <Box>
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                fontWeight="bold"
              >
                Progress Review for Deliverables
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Track milestone progress, payments, and delivery status
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              {!isEditing ? (
                <>
                  <Button
                    variant="contained"
                    startIcon={<EditIcon />}
                    onClick={() => setIsEditing(true)}
                    sx={{ bgcolor: "primary.main" }}
                  >
                    Edit Mode
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={addMilestone}
                    sx={{
                      bgcolor: "success.main",
                      "&:hover": { bgcolor: "success.dark" },
                    }}
                  >
                    Add Milestone
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="contained"
                    startIcon={<CheckIcon />}
                    onClick={handleSubmit(onSubmit)}
                    disabled={!isValid}
                    sx={{
                      bgcolor: "success.main",
                      "&:hover": { bgcolor: "success.dark" },
                    }}
                  >
                    Save All
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<CloseIcon />}
                    onClick={toggleEdit}
                    sx={{
                      bgcolor: "error.main",
                      "&:hover": { bgcolor: "error.dark" },
                    }}
                  >
                    Cancel
                  </Button>
                </>
              )}
            </Stack>
          </Box>

          {/* Summary Stats */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Card
                variant="outlined"
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  borderColor: "primary.main",
                }}
              >
                <CardContent
                  sx={{ display: "flex", alignItems: "center", gap: 2 }}
                >
                  <FileTextIcon color="primary" />
                  <Box>
                    <Typography variant="body2" color="primary.main">
                      Total Milestones
                    </Typography>
                    <Typography
                      variant="h4"
                      fontWeight="bold"
                      color="primary.main"
                    >
                      {fields.length}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card
                variant="outlined"
                sx={{
                  bgcolor: alpha(theme.palette.success.main, 0.1),
                  borderColor: "success.main",
                }}
              >
                <CardContent
                  sx={{ display: "flex", alignItems: "center", gap: 2 }}
                >
                  <MoneyIcon color="success" />
                  <Box>
                    <Typography variant="body2" color="success.main">
                      Total Payment Due
                    </Typography>
                    <Typography
                      variant="h4"
                      fontWeight="bold"
                      color="success.main"
                    >
                      {formatCurrency(totalPaymentDue)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card
                variant="outlined"
                sx={{
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  borderColor: "warning.main",
                }}
              >
                <CardContent
                  sx={{ display: "flex", alignItems: "center", gap: 2 }}
                >
                  <CalendarIcon color="warning" />
                  <Box>
                    <Typography variant="body2" color="warning.main">
                      Completed Milestones
                    </Typography>
                    <Typography
                      variant="h4"
                      fontWeight="bold"
                      color="warning.main"
                    >
                      {watchedMilestones.filter((m) => m.achievedDate).length}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Table */}
      <TableContainer component={Paper} elevation={2}>
        <Table>
          <TableHead sx={{ bgcolor: "#f5f5f5" }}>
            <TableRow>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Milestone
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Due date as per contract
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Due date as planned
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Achieved date
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Payment due
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Invoice date
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Payment received date
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                Comments
              </TableCell>
              {isEditing && (
                <TableCell sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
                  Actions
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {fields.map((field, index) => (
              <TableRow
                key={field.id}
                hover
                sx={{ "&:nth-of-type(odd)": { bgcolor: "action.hover" } }}
              >
                {/* Milestone */}
                <TableCell sx={{ minWidth: 200 }}>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.milestone`)}
                      multiline
                      rows={2}
                      fullWidth
                      size="small"
                      error={hasFieldError(`milestones.${index}.milestone`)}
                      helperText={getFieldError(
                        `milestones.${index}.milestone`
                      )}
                      placeholder="Enter milestone"
                      variant="outlined"
                    />
                  ) : (
                    <Typography variant="body2" fontWeight="medium">
                      {watchedMilestones[index]?.milestone || "-"}
                    </Typography>
                  )}
                </TableCell>

                {/* Due date as per contract */}
                <TableCell>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.dueDateContract`)}
                      type="date"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      fullWidth
                    />
                  ) : (
                    <Typography variant="body2">
                      {formatDate(watchedMilestones[index]?.dueDateContract)}
                    </Typography>
                  )}
                </TableCell>

                {/* Due date as planned */}
                <TableCell>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.dueDatePlanned`)}
                      type="date"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      fullWidth
                    />
                  ) : (
                    <Typography variant="body2">
                      {formatDate(watchedMilestones[index]?.dueDatePlanned)}
                    </Typography>
                  )}
                </TableCell>

                {/* Achieved date */}
                <TableCell>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.achievedDate`)}
                      type="date"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      fullWidth
                    />
                  ) : (
                    <Chip
                      label={formatDate(watchedMilestones[index]?.achievedDate)}
                      color={
                        watchedMilestones[index]?.achievedDate
                          ? "success"
                          : "default"
                      }
                      size="small"
                      variant="outlined"
                    />
                  )}
                </TableCell>

                {/* Payment due */}
                <TableCell>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.paymentDue`, {
                        valueAsNumber: true,
                      })}
                      type="number"
                      size="small"
                      inputProps={{ step: "0.01" }}
                      placeholder="0.00"
                      fullWidth
                    />
                  ) : (
                    <Typography
                      variant="body2"
                      color="success.main"
                      fontWeight="medium"
                    >
                      {formatCurrency(watchedMilestones[index]?.paymentDue)}
                    </Typography>
                  )}
                </TableCell>

                {/* Invoice date */}
                <TableCell>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.invoiceDate`)}
                      type="date"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      fullWidth
                    />
                  ) : (
                    <Typography variant="body2">
                      {formatDate(watchedMilestones[index]?.invoiceDate)}
                    </Typography>
                  )}
                </TableCell>

                {/* Payment received date */}
                <TableCell>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.paymentReceivedDate`)}
                      type="date"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      fullWidth
                    />
                  ) : (
                    <Chip
                      label={getPaymentStatus(
                        watchedMilestones[index]?.paymentDue,
                        watchedMilestones[index]?.paymentReceivedDate
                      )}
                      color={getPaymentStatusColor(
                        getPaymentStatus(
                          watchedMilestones[index]?.paymentDue,
                          watchedMilestones[index]?.paymentReceivedDate
                        )
                      )}
                      size="small"
                      variant="outlined"
                    />
                  )}
                </TableCell>

                {/* Comments */}
                <TableCell sx={{ minWidth: 200 }}>
                  {isEditing ? (
                    <TextField
                      {...register(`milestones.${index}.comments`)}
                      multiline
                      rows={2}
                      fullWidth
                      size="small"
                      placeholder="Enter comments"
                      variant="outlined"
                    />
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      {watchedMilestones[index]?.comments || "-"}
                    </Typography>
                  )}
                </TableCell>

                {/* Actions */}
                {isEditing && (
                  <TableCell>
                    <Tooltip title="Delete milestone">
                      <IconButton
                        onClick={() => removeMilestone(index)}
                        color="error"
                        size="small"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Empty State */}
        {fields.length === 0 && (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={8}
          >
            <FileTextIcon
              sx={{ fontSize: 48, color: "text.secondary", mb: 2 }}
            />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No milestones added yet
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={addMilestone}
              sx={{ mt: 2 }}
            >
              Add First Milestone
            </Button>
          </Box>
        )}
      </TableContainer>

      {/* Validation Errors */}
      {Object.keys(errors).length > 0 && (
        <Alert severity="error" sx={{ mt: 3 }} icon={<AlertIcon />}>
          <Typography variant="body2" fontWeight="medium">
            Validation Errors
          </Typography>
          <Typography variant="body2">
            Please fix the errors in the form before saving.
          </Typography>
        </Alert>
      )}

      {/* Form Status */}
      <Card sx={{ mt: 3 }} variant="outlined">
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Form Status
          </Typography>
          <Stack spacing={1}>
            <Typography variant="body2">
              Valid: {isValid ? "✅" : "❌"}
            </Typography>
            <Typography variant="body2">
              Total Errors: {Object.keys(errors).length}
            </Typography>
            <Typography variant="body2">
              Dirty: {isDirty ? "✅" : "❌"}
            </Typography>
          </Stack>
        </CardContent>
      </Card>
    </Container>
  );
};

export default ProgressReviewDeliverables;
